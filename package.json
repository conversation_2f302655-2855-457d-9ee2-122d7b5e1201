{"name": "com.mydoginfit.app", "private": true, "version": "0.0.0", "type": "module", "scripts": {"_css": "npx tailwindcss -i ./tailwind.css -o ./assets/tailwind.css --optimize --watch=always", "dev-android": "dx serve --hot-patch --platform android", "_dev-android": "npm run _css &> /dev/null & pid=$! && npm run _dev-android && kill $pid", "dev-ios": "dx serve --hot-patch --platform ios --target x86_64-apple-ios", "_dev-ios": "npm run _css &> /dev/null & pid=$! && npm run _dev-ios && kill $pid", "dev-web": "dx serve --platform web", "_dev-web": "npm run _css &> /dev/null & pid=$! && npm run _dev-web && kill $pid", "emu": "QT_QPA_PLATFORM='' emulator -avd Pixel_6_API_34 -netdelay none -netspeed full", "emu-ios": "xcrun simctl boot 'iPhone 16e'", "_log": "adb logcat -s dioxus:V", "log-android": "adb logcat -s RustStdoutStderr | grep -v s_glBind | sed -r -e 's/([0-9-]+ [0-9:.]+) ([0-9]+ [0-9]+) I RustStdoutStderr: (.*)/\\1 \\3/g'"}, "dependencies": {}, "devDependencies": {"@tailwindcss/cli": "^4.1.11", "@tailwindcss/postcss": "^4.1.11", "@tailwindcss/typography": "^0.5.16", "tailwind-merge": "^3.3.1", "tailwindcss": "^4.1.11", "tailwindcss-animate": "^1.0.7"}}