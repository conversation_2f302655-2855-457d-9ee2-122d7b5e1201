use dioxus::{
    logger::tracing::info,
    prelude::*,
};
use dioxus_radio::prelude::*;

use crate::{
    i18n::*,
    models::*,
    ui::routes::Route,
};


const APP_NAME: &str = "My Dog in Fit";
const FAVICON: Asset = asset!("/assets/favicon.ico");
// const MAIN_CSS: Asset = asset!("/assets/styling/main.css");
const TAILWIND_CSS: Asset = asset!(
    "/assets/tailwind.css",
    CssAssetOptions::new().with_minify(
        #[cfg(debug_assertions)]
        false,
        #[cfg(not(debug_assertions))]
        true,
    )
);


#[component]
pub fn App() -> Element {
    info!("App()");
    use_init_radio_station::<AppState, AppEvent>(AppState::default);
    use_context_provider(AppState::default);

    rsx! {
        document::Meta {
            name: "viewport",
            // content: "width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no, viewport-fit=cover",
            content: "width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no, viewport-fit=cover, interactive-widget=resizes-content",
        }
        document::Meta { name: "mobile-web-app-capable", content: "yes" }
        document::Meta { name: "apple-mobile-web-app-capable", content: "yes" }
        document::Meta {
            name: "apple-mobile-web-app-status-bar-style",
            content: "black-translucent",
        }
        document::Meta {
            name: "format-detection",
            content: "telephone=no, date=no, address=no, email=no",
        }
        document::Meta { name: "msapplication-TileColor", content: "#ffffff" }
        document::Meta {
            name: "msapplication-TileImage",
            content: "/icons/icon-192x192.png",
        }
        document::Meta { name: "msapplication-config", content: "/icons/browserconfig.xml" }
        document::Meta { name: "apple-touch-fullscreen", content: "yes" }
        document::Meta { name: "apple-mobile-web-app-title", content: "{APP_NAME}" }
        document::Meta { name: "application-name", content: "{APP_NAME}" }
        document::Meta {
            name: "description",
            content: "Track your dog's fitness and health",
        }
        document::Meta {
            name: "theme-color",
            // content: "hsl(var(--color-primary))",
            content: "#f7f8ff",
        }
        document::Meta { name: "color-scheme", content: "light dark" }

        document::Link { rel: "icon", href: FAVICON }
        // document::Stylesheet { rel: "stylesheet", href: MAIN_CSS }
        document::Stylesheet { rel: "stylesheet", href: TAILWIND_CSS }

        AppLayout {}
    }
}

#[component]
pub fn AppLayout() -> Element {
    info!("AppLayout()");
    rsx! {
        div { class: "mx-auto absolute top-0 bottom-0 left-0 right-0",
            // style: "padding-top: calc(env(safe-area-inset-top, 0px) + 8px); padding-bottom: env(safe-area-inset-bottom); padding-left: env(safe-area-inset-left); padding-right: env(safe-area-inset-right);",
            // style: "padding-top: 24px; padding-bottom: env(safe-area-inset-bottom);",
            // class: "{getPageGradientClass()}",
            // Toaster {}
            // Sonner {}
            I18nProvider { Router::<Route> {} }
        }
    }
}
