use dioxus::prelude::*;
use dioxus_motion::prelude::*;

use crate::ui::{
    layout::BottomNavbar,
    pages::{
        // ActivityHistory,
        // AddDog,
        // DogProfile,
        Home,
        NotFound,
        Onboarding,
        // Results,
        Settings,
        WeightHistory,
    },
};

#[derive(Routable, <PERSON>lone, PartialEq, MotionTransitions)]
#[rustfmt::skip]
pub enum Route {
    #[layout(WithNavbar)]
        #[route("/")]
        #[transition(Fade)]
        Home {},
        #[route("/settings")]
        #[transition(Fade)]
        Settings {},
    #[end_layout]

    // #[route("/activities/history")]
    // ActivityHistory {},
    // #[route("/add-dog")]
    // AddDog {},
    // #[route("/ai")]
    // AIChat {},
    // #[route("/dog/:id")]
    // DogProfile { id: String },
    #[route("/onboarding")]
    Onboarding {},
    // #[route("/results")]
    // ResultsPage {},
    #[route("/weight-history")]
    WeightHistory {},

    #[route("/*path")]
    NotFound { path: String },
}

#[component]
pub fn WithNavbar() -> Element {
    rsx! {
      AnimatedOutlet::<Route> {}
      BottomNavbar {}
    }
}
