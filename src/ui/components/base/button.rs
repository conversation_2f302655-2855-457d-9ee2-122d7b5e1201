use dioxus::prelude::*;

#[derive(<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>)]
pub enum ButtonVariant {
    Default,
    Destructive,
    Outline,
    Secondary,
    Ghost,
    <PERSON>,
    Glow,
    Glass,
}

#[derive(<PERSON>ps, PartialEq, Clone)]
pub struct ButtonProps {
    #[props(default)]
    pub children: Element,
    #[props(default)]
    pub class:    String,
    #[props(optional)]
    pub color:    Option<String>,
    #[props(default = false)]
    pub disabled: bool,
    #[props(default)]
    pub onclick:  EventHandler<MouseEvent>,
    #[props(default = ButtonVariant::Default)]
    pub variant:  ButtonVariant,
    #[props(default = "default".to_string())]
    pub size:     String,
}


#[component]
pub fn Button(props: ButtonProps) -> Element {
    // let base_class = "inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm \
    //                   font-medium ring-offset-background transition-colors focus-visible:outline-none \
    //                   focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 \
    //                   disabled:pointer-events-none disabled:opacity-50";

    let variant_class = match props.variant {
        ButtonVariant::Destructive => "text-destructive-foreground active:scale-[0.98]",
        ButtonVariant::Outline => "border border-input hover:text-accent-foreground active=>scale-[0.98]",
        ButtonVariant::Secondary => "text-secondary-foreground active=>scale-[0.98]",
        ButtonVariant::Ghost => "hover:text-accent-foreground",
        ButtonVariant::Link => "text-primary underline-offset-4 hover:underline",
        ButtonVariant::Glow =>
            "text-primary-foreground active:scale-[0.98] shadow-glow-sm hover:shadow-glow-md \
             dark:shadow-glow-sm dark:hover:shadow-glow-md",
        ButtonVariant::Glass =>
            "backdrop-blur-md text-primary active:scale-[0.98] border border-white/20 dark:text-white \
             dark:border-white/10 colored:text-white colored:border-white/20",
        _ => "backdrop-blur-sm text-white active:scale-[0.98] hover:shadow-xs",
    };
    let color = if props.color.is_some() {
        props.color.as_deref().unwrap()
    } else {
        match props.variant {
            ButtonVariant::Destructive => "bg-destructive",
            ButtonVariant::Ghost => "bg-transparent",
            ButtonVariant::Glass => "bg-white/70 dark:bg-black/30 colored:bg-white/20",
            ButtonVariant::Glow => "bg-primary",
            ButtonVariant::Link => "bg-transparent",
            ButtonVariant::Outline => "bg-transparent",
            ButtonVariant::Secondary => "bg-secondary",
            _ => "bg-secondary",
        }
    };
    let color_hover = if props.color.is_some() {
        format!("{}/90", props.color.as_deref().unwrap())
    } else {
        match props.variant {
            ButtonVariant::Destructive => "bg-destructive/80".to_string(),
            ButtonVariant::Ghost => "bg-accent/80".to_string(),
            ButtonVariant::Glass => "bg-white/80 dark:bg-black/40 colored:bg-white/30".to_string(),
            ButtonVariant::Glow => "bg-primary/90".to_string(),
            ButtonVariant::Link => "bg-transparent".to_string(),
            ButtonVariant::Outline => "bg-accent/80".to_string(),
            ButtonVariant::Secondary => "bg-secondary/80".to_string(),
            _ => "bg-secondary/90".to_string(),
        }
    };

    let size_class = match props.size.as_str() {
        "sm" => "h-9 rounded-xl px-3",
        "lg" => "h-11 px-8",
        "icon" => "h-10 w-10",
        _ => "h-10 px-4 py-2", // default
    };

    rsx! {
        button {
            class: "inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-full text-sm font-medium ring-offset-background transition-all duration-300 focus-visible:outline-hidden focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0 [&_svg]:transition-transform [&_svg]:duration-300 {variant_class} {size_class} {props.class} {color} hover:{color_hover}",
            onclick: move |evt| props.onclick.call(evt),
            {props.children}
        }
    }
}
