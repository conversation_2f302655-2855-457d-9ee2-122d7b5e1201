# npm i -D tailwindcss @tailwindcss/cli
# npx tailwindcss -i ./tailwind.css -o ./assets/tailwind.css --watch

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html
[package]
name = "my-dog-in-fit"
version = "0.1.0"
authors = ["Alexandr Priezzhev <<EMAIL>>"]
edition = "2024"


[dependencies]
base64 = "0.22"
# async-trait = "0.1.80"
chrono = { version = "0.4", features = ["serde"] }
# daisy_rsx = { git = "https://github.com/tsr8/daisy-rsx.git", branch = "daisy-5.0.6" }
# dioxus = { version = "0.6.3", features = [
dioxus = { version = "0.7.0-alpha.2", features = [
  "router",
  # "fullstack",
  "logger",
  # "mobile",
  # "native",
] }
dioxus-free-icons = { git = "https://github.com/Raflos10/dioxus-free-icons", features = [
  "font-awesome-solid",
  "lucide",
] }
dioxus-i18n = "0.4.4"
dioxus-motion = { git = "https://github.com/wheregmis/dioxus-motion.git", branch = "main", optional = true }
dioxus-primitives = { git = "https://github.com/dioxuslabs/components", version = "0.0.1" }
dioxus-radio = "0.6.0"
# dioxus_storage = "0.0.4"
dioxus_storage = { git = "https://github.com/ealmloff/dioxus-std", branch = "0.7" }
dotenvy = "0.15.7"
gloo-storage = "0.3.0"
# dioxus-time = { version = "0.7.0-alpha.2" }
fluent = "0.17.0"
fluent-bundle = "0.16.0"
# fluent-templates = "0.13.0"
image = { version = "0.25", features = ["jpeg", "png"], optional = true }
postgrest = "1.6.0"
# rand = "0.9.1"
reqwest = { version = "0.12.22", features = ["json"], optional = true }
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
tokio = { version = "1.0", features = ["time"] }
unic-langid = "0.9.6"
modx = "0.1.4"
# reaxive = "1.0.3"
# uuid = { version = "1.8.0", features = ["v4"] }
# uuid = { version = "1.8.0", features = ["v4", "fast-rng", "macro-diagnostics"] }
# web-sys = { version = "0.3", features = ["Window", "Storage", "console"] }

[target.'cfg(target_os = "android")'.dependencies]
# aloe-android-notifications = "0.1.2"
# dioxus = { version = "0.7.0-alpha.2", features = [
#   "router",
#   # "fullstack",
#   "logger",
#   "mobile",
#   # "native",
# ] }

# [target.'cfg(target_os = "macos")'.dependencies]
# cocoa = "0.25.0" # cocoa::appkit is deprecated in 0.26.1

[target.'cfg(target_os = "ios")'.dependencies]
# aloe-notifications = "0.1.1"
# dioxus = { version = "0.7.0-alpha.2", features = [
#   "router",
#   # "fullstack",
#   "logger",
#   "mobile",
#   # "native",
# ] }

[patch.crates-io]
# aloe-3p = { path = '../../../Modules/aloe-rs/aloe-3p' }
# aloe-url = { path = '../../../Modules/aloe-rs/aloe-url' }
dioxus-i18n = { path = '../../../Modules/dioxus-i18n' }
dioxus-radio = { path = '../../../Modules/dioxus-radio' }
# dioxus-sdk = { git = "https://github.com/ealmloff/dioxus-std", branch = "0.7" }
# dioxus_storage = { git = "https://github.com/ealmloff/dioxus-std", branch = "0.7" }
# dioxus-sync = { git = "https://github.com/ealmloff/dioxus-std", branch = "0.7" }
dioxus-time = { git = "https://github.com/ealmloff/dioxus-std", branch = "0.7" }
# dioxus-util = { git = "https://github.com/ealmloff/dioxus-std", branch = "0.7" }

# [replace]
# "cocoa:0.25.0" = { git = "https://github.com/servo/core-foundation-rs.git", branch = "main", package = "cocoa" }


[features]
default = ["dioxus-motion/transitions"]
# mobile = ["dioxus/native"]
server = ["dioxus/server"]
web = ["dioxus/web", "dioxus-motion/web", "dioxus-motion/transitions"]
desktop = [
  "dioxus/desktop",
  "dioxus-motion/desktop",
  "dioxus-motion/transitions",
]
mobile = ["dioxus/mobile", "dioxus-motion/desktop", "dioxus-motion/transitions"]


[workspace]
members = ["."]


[profile.release]
# opt-level = "z"
opt-level = 3
debug = false
lto = true
codegen-units = 1
panic = "abort"
strip = true
incremental = false

[profile.dev]
opt-level = 0
debug = 1
incremental = true
lto = "off"
codegen-units = 256
# TODO codegen-backend = "cranelift"

[profile.dev.package."*"]
opt-level = 1 # Page transitions do not work with 2 or 3
debug = 2

# * For build scripts and proc-macros.
[profile.dev.build-override]
opt-level = 3


# * Web
[profile.wasm-dev]
inherits = "dev"
# opt-level = 1

[profile.wasm-dev.package."*"]
opt-level = 1 # Page transitions do not work with 2 or 3
debug = 2

[profile.wasm-dev.build-override]
opt-level = 3


# * Server
[profile.server-dev]
inherits = "dev"
# opt-level = 1

[profile.server-dev.package."*"]
opt-level = 3
debug = 2

[profile.server-dev.build-override]
opt-level = 3


# * Android
[profile.android-dev]
inherits = "dev"

[profile.android-dev.package."*"]
opt-level = 1 # Page transitions do not work with 2 or 3
debug = 2

[profile.android-dev.build-override]
opt-level = 3


# * iOS
[profile.ios-dev]
inherits = "dev"

[profile.ios-dev.package."*"]
opt-level = 1 # Page transitions do not work with 2 or 3
debug = 2

[profile.ios-dev.build-override]
opt-level = 3
